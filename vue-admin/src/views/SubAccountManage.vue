<template>
  <div class="page-container">
    <!-- 搜索筛选区域 -->
    <div class="search-section">
      <div class="layui-card main-card">
        <div class="layui-card-header">子账号管理</div>
        <div class="layui-card-body">
          <div class="list_search">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-input
                  v-model="searchForm.account"
                  placeholder="搜索账号"
                  clearable
                />
              </el-col>
              <el-col :span="6">
                <el-select
                  v-model="searchForm.status"
                  placeholder="账号状态"
                  clearable
                  style="width: 100%"
                >
                  <el-option label="正常" value="normal" />
                  <el-option label="禁用" value="disabled" />
                </el-select>
              </el-col>
              <el-col :span="6">
                <el-select
                  v-model="searchForm.online"
                  placeholder="是否在线"
                  clearable
                  style="width: 100%"
                >
                  <el-option label="在线" value="online" />
                  <el-option label="离线" value="offline" />
                </el-select>
              </el-col>
              <el-col :span="6">
                <el-button type="primary" @click="handleSearch" class="btn2">
                  <el-icon><Search /></el-icon>
                  搜索
                </el-button>
                <el-button @click="handleReset" class="btn6">
                  <el-icon><Refresh /></el-icon>
                  重置
                </el-button>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
    </div>

    <!-- 表格和分页容器 -->
    <div class="table-section">
      <div class="layui-card main-card table-card">
        <div class="layui-card-body">
          <!-- 表格容器 -->
          <div class="table-wrapper p-4">
            <el-table
              :data="tableData"
              style="width: 100%"
              stripe
              border
              :max-height="tableMaxHeight"
              class="stable-table"
              @row-contextmenu="handleRowContextMenu"
            >
              <el-table-column prop="account" label="账号" min-width="80" />
              <el-table-column prop="level" label="等级" min-width="60" />
              <el-table-column prop="balance" label="余额" min-width="70" />
              <el-table-column prop="expireTime" label="到期时间" min-width="100" />
              <el-table-column prop="status" label="状态" min-width="60">
                <template #default="scope">
                  <el-tag
                    :type="scope.row.status === '正常' ? 'success' : 'danger'"
                    size="small"
                  >
                    {{ scope.row.status }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="loginTime" label="登陆时间" min-width="100" />
              <el-table-column prop="online" label="在线" min-width="50">
                <template #default="scope">
                  <el-tag
                    :type="scope.row.online === '在线' ? 'success' : 'info'"
                    size="small"
                  >
                    {{ scope.row.online }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="storeCount" label="店铺" min-width="50" />
              <el-table-column prop="onlineStoreCount" label="在线店铺" min-width="70" />
              <!-- 操作列暂时隐藏 -->
              <!-- <el-table-column label="操作" width="200" fixed="right">
                <template #default="scope">
                  <el-button
                    size="small"
                    type="primary"
                    @click="handleEdit(scope.$index, scope.row)"
                    class="btn2"
                  >
                    编辑
                  </el-button>
                  <el-button
                    size="small"
                    type="danger"
                    @click="handleDelete(scope.$index, scope.row)"
                    class="btn4"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column> -->
            </el-table>
          </div>

          <!-- 分页容器 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="total"
              layout="prev, pager, next"
              prev-text="上一页"
              next-text="下一页"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              class="layui-pagination"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 右键菜单 -->
    <ContextMenu
      ref="contextMenuRef"
      @menu-click="handleContextMenuClick"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
import ContextMenu from '../components/ui/ContextMenu.vue'

// 搜索表单
const searchForm = ref({
  account: '',
  status: '',
  online: ''
})

// 表格数据
const tableData = ref([
  {
    account: '184762',
    level: 0,
    balance: '999.00',
    expireTime: '2025/7/5 21:04:34',
    status: '禁用',
    loginTime: '2025/7/5 21:04:34',
    online: '离线',
    storeCount: 1,
    onlineStoreCount: 0
  },
  {
    account: '184763',
    level: 0,
    balance: '999.00',
    expireTime: '2025/7/5 21:04:34',
    status: '正常',
    loginTime: '2025/7/5 21:04:34',
    online: '离线',
    storeCount: 1,
    onlineStoreCount: 0
  },
  {
    account: '184764',
    level: 1,
    balance: '1999.00',
    expireTime: '2025/8/5 21:04:34',
    status: '正常',
    loginTime: '2025/7/5 21:04:34',
    online: '在线',
    storeCount: 3,
    onlineStoreCount: 2
  },
  {
    account: '184765',
    level: 2,
    balance: '2999.00',
    expireTime: '2025/9/5 21:04:34',
    status: '正常',
    loginTime: '2025/7/5 21:04:34',
    online: '在线',
    storeCount: 5,
    onlineStoreCount: 4
  }
])

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)

// 右键菜单
const contextMenuRef = ref(null)

// 计算表格最大高度
const tableMaxHeight = computed(() => {
  // 基础高度：视口高度减去头部、搜索区域、分页区域等固定高度
  const baseHeight = window.innerHeight - 300 // 预留300px给其他元素
  return Math.max(400, Math.min(600, baseHeight)) // 最小400px，最大600px
})
// 搜索
const handleSearch = () => {
  console.log('搜索:', searchForm.value)

  // 调用客户端函数
  if (window.g_click) {
    window.g_click({
      request: JSON.stringify({
        action: "子账号管理搜索按钮",
        text: JSON.stringify(searchForm.value)
      })
    })
  }

  ElMessage.success('搜索功能待实现')
}

// 重置
const handleReset = () => {
  searchForm.value = {
    account: '',
    status: '',
    online: ''
  }

  // 调用客户端函数
  if (window.g_click) {
    window.g_click({
      request: JSON.stringify({
        action: "子账号管理重置按钮"
      })
    })
  }

  ElMessage.info('已重置搜索条件')
}

// 编辑
const handleEdit = (index, row) => {
  console.log('编辑:', index, row)
  ElMessage.info('编辑功能待实现')
}

// 删除
const handleDelete = (index, row) => {
  ElMessageBox.confirm(
    `确定要删除账号 ${row.account} 吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    tableData.value.splice(index, 1)
    ElMessage.success('删除成功')
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 分页事件
const handleSizeChange = (val) => {
  pageSize.value = val
  console.log(`每页 ${val} 条`)

  // 调用客户端函数
  if (window.g_click) {
    window.g_click({
      request: JSON.stringify({
        action: "子账号管理分页大小改变",
        pageSize: val
      })
    })
  }
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  console.log(`当前页: ${val}`)

  // 调用客户端函数
  if (window.g_click) {
    window.g_click({
      request: JSON.stringify({
        action: "子账号管理分页按钮",
        page: val
      })
    })
  }
}

// 右键菜单事件处理
const handleRowContextMenu = (row, column, event) => {
  event.preventDefault()

  if (contextMenuRef.value) {
    contextMenuRef.value.show(event.clientX, event.clientY, row)
  }
}

// 处理右键菜单点击
const handleContextMenuClick = ({ action, data }) => {
  // 这里的逻辑已经在ContextMenu组件中处理了
  // 包括调用window.g_click和显示消息提示
}

// 设置全局变量
const setupGlobalVariables = () => {
  // 设置表格数据到全局变量
  window.table_zizhanghao = tableData.value
  window.table_zizhanghao_currentPage = currentPage.value
  window.table_zizhanghao_pageSize = pageSize.value
  window.table_zizhanghao_total = total.value
}

// 监听数据变化并更新全局变量
watch([tableData, currentPage, pageSize, total], () => {
  setupGlobalVariables()
}, { deep: true })

// 从全局变量更新数据
const updateFromGlobalData = () => {
  if (window.table_zizhanghao && Array.isArray(window.table_zizhanghao)) {
    tableData.value = window.table_zizhanghao
  }
  if (window.table_zizhanghao_currentPage) {
    currentPage.value = window.table_zizhanghao_currentPage
  }
  if (window.table_zizhanghao_pageSize) {
    pageSize.value = window.table_zizhanghao_pageSize
  }
  if (window.table_zizhanghao_total) {
    total.value = window.table_zizhanghao_total
  }
}

// 暴露更新函数到全局
window.updateSubAccountData = updateFromGlobalData

onMounted(() => {
  // 初始化数据
  setupGlobalVariables()
})
</script>

<style scoped>
/* 页面容器布局 */
.page-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 64px); /* 减去头部导航高度，贴近底部 */
  padding: 20px 0px 20px 20px; /* 底部添加padding */
  box-sizing: border-box;
  margin-top: -32px;
}

.search-section {
  flex-shrink: 0;
  margin-bottom: 15px;
}

.table-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 重要：允许flex子元素收缩 */
}

.table-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.table-card .layui-card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  padding: 16px;
}

/* 表格容器 */
.table-wrapper {
  flex: 1;
  min-height: 0;
  margin-bottom: 16px;
}

.stable-table {
  height: 100%;
}

/* 分页容器 */
.pagination-wrapper {
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px 0;
  border-top: 1px solid #e5e7eb;
  
}

/* Element Plus 样式覆盖 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header) {
  background-color: #f8f9fa;
}

:deep(.stable-table .el-table__body-wrapper) {
  overflow-y: auto;
  max-height: calc(100% - 60px); /* 减去表头高度 */
}

/* 超紧凑型表格样式 */
:deep(.el-table__row) {
  height: 28px !important; /* 极紧凑的行高 */
}

:deep(.el-table__cell) {
  padding: 2px 4px !important; /* 极小化单元格padding */
  line-height: 1.0;
  font-size: 12px;
  vertical-align: middle;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

:deep(.el-table__header .el-table__cell) {
  padding: 4px 4px !important;
  font-weight: 600;
  font-size: 12px;
  height: 30px !important;
}

/* 超紧凑型标签样式 */
:deep(.el-tag) {
  margin: 0;
  padding: 0px 3px;
  font-size: 10px;
  height: 16px;
  line-height: 16px;
  border-radius: 2px;
}

/* 表格整体超紧凑化 */
:deep(.el-table) {
  font-size: 12px;
  --el-table-border-color: #e4e7ed;
  --el-table-border: 1px solid var(--el-table-border-color);
}

:deep(.el-table__body) {
  border-spacing: 0;
}

/* 减少表格边框间距 */
:deep(.el-table td.el-table__cell) {
  border-bottom: 1px solid #ebeef5;
  border-right: 1px solid #f0f0f0;
}

:deep(.el-table th.el-table__cell) {
  border-bottom: 1px solid #ebeef5;
  border-right: 1px solid #f0f0f0;
}

/* 表格内容超紧凑显示 */
:deep(.el-table__body-wrapper) {
  scrollbar-width: thin;
}

:deep(.el-table .cell) {
  padding: 0 !important;
  word-break: keep-all;
}

/* 表格自适应宽度 */
:deep(.stable-table) {
  width: 100%;
  table-layout: auto; /* 允许自适应 */
}

:deep(.stable-table .el-table__header-wrapper) {
  width: 100%;
}

:deep(.stable-table .el-table__body-wrapper) {
  width: 100%;
}

/* 确保表格列能够自适应剩余空间 */
:deep(.el-table__header colgroup col) {
  width: auto !important;
}

:deep(.el-table__body colgroup col) {
  width: auto !important;
}

:deep(.el-button.btn2) {
  background-color: #7748F8;
  border-color: #7748F8;
  color: #fff;
}

:deep(.el-button.btn2:hover) {
  background-color: #6639e6;
  border-color: #6639e6;
}

:deep(.el-button.btn4) {
  background-color: #E67162;
  border-color: #E67162;
  color: #fff;
}

:deep(.el-button.btn4:hover) {
  background-color: #d85a4a;
  border-color: #d85a4a;
}

:deep(.el-button.btn6) {
  border: 1px solid #9494AA;
  color: #9494AA;
  
}

:deep(.el-button.btn6:hover) {
  background-color: #f5f5f5;
}

/* Layui 风格分页样式 */
:deep(.layui-pagination) {
  justify-content: flex-end;
}

:deep(.layui-pagination .btn-next),
:deep(.layui-pagination .btn-prev) {
  background-color: #fff;
  border: 1px solid #e2e2e2;
  color: #333;
  border-radius: 2px;
  padding: 0 15px;
  height: 30px;
  line-height: 28px;
  font-size: 12px;
}

:deep(.layui-pagination .btn-next:hover),
:deep(.layui-pagination .btn-prev:hover) {
  color: #7748F8;
  border-color: #7748F8;
}

:deep(.layui-pagination .btn-next.is-disabled),
:deep(.layui-pagination .btn-prev.is-disabled) {
  color: #c0c4cc;
  background-color: #fff;
  border-color: #e2e2e2;
  cursor: not-allowed;
}

:deep(.layui-pagination .el-pager li) {
  background-color: #fff;
  border: 1px solid #e2e2e2;
  color: #333;
  border-radius: 2px;
  margin: 0 2px;
  min-width: 30px;
  height: 30px;
  line-height: 28px;
  font-size: 12px;
}

:deep(.layui-pagination .el-pager li:hover) {
  color: #7748F8;
  border-color: #7748F8;
}

:deep(.layui-pagination .el-pager li.is-active) {
  background-color: #7748F8;
  border-color: #7748F8;
  color: #fff;
}

:deep(.layui-pagination .el-pager li.is-active .layui-laypage-em) {
  position: absolute;
  left: -1px;
  top: -1px;
  padding: 1px;
  width: 100%;
  height: 100%;
  background-color: #7748F8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 10px;
    min-height: calc(100vh - 80px);
  }

  .pagination-wrapper {
    padding: 12px 0;
  }

  :deep(.layui-pagination) {
    justify-content: center;
  }

  :deep(.el-pagination) {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .table-wrapper {
    overflow-x: auto;
  }

  :deep(.stable-table) {
    min-width: 800px;
  }
}
</style>
